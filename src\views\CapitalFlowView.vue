<template>
  <div class="capital-flow-container">
    <h1>资金流动分析</h1>

    <!-- 时间段选择 -->
    <div class="time-selector-section">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        :shortcuts="dateShortcuts"
        @change="handleDateChange"
        size="large"
        style="width: 300px"
      />
      <el-button type="primary" @click="searchData" size="large">
        <el-icon><Search /></el-icon> 查询
      </el-button>
      <el-button @click="resetFilters" size="large">
        <el-icon><Refresh /></el-icon> 重置
      </el-button>
    </div>

    <!-- 资金情况卡片 -->
    <div class="fund-status-section">
      <div class="section-header">
        <h2>资金情况</h2>
      </div>
      <div class="fund-status-table" v-loading="loading">
        <VTableComponent
          v-if="fundStatusData.length > 0"
          :data="fundStatusData"
          :width="800"
          :height="200"
          :show-filter="false"
          :editable="false"
          :enable-copy-paste="true"
          :auto-width="true"
        />
        <div v-else class="no-data">请选择日期范围并点击查询按钮获取数据</div>
      </div>
    </div>

    <!-- 收款情况 -->
    <div class="receipt-section">
      <div class="section-header">
        <h2>收款情况</h2>
      </div>
      <div class="receipt-table" v-loading="loading">
        <VTableComponent
          v-if="receiptData.length > 0"
          :data="receiptData"
          :width="600"
          :height="300"
          :show-filter="false"
          :editable="false"
          :enable-copy-paste="true"
          :auto-width="true"
        />
        <div v-else class="no-data">暂无收款数据</div>
      </div>
    </div>

    <!-- 付款情况 - 二维表格 -->
    <div class="payment-section">
      <div class="section-header">
        <h2>付款情况</h2>
      </div>
      <div class="payment-table" v-loading="loading">
        <VTableComponent
          v-if="paymentPivotData.length > 0"
          :data="paymentPivotData"
          :width="900"
          :height="350"
          :show-filter="false"
          :editable="false"
          :enable-copy-paste="true"
          :auto-width="true"
        />
        <div v-else class="no-data">暂无付款数据</div>
      </div>
    </div>

    <!-- 资金分布饼图 -->
    <div class="chart-section">
      <div class="section-header">
        <h2>资金分布</h2>
      </div>
      <div class="chart-container">
        <div id="fundDistributionChart" class="chart"></div>
      </div>
    </div>

    <!-- 在途付款单据 -->
    <div class="pending-payment-section">
      <div class="section-header">
        <h2>在途付款单据</h2>
        <div class="summary-info">
          <span class="total-amount">总额: ¥{{ formatNumber(pendingPaymentTotal) }}</span>
        </div>
      </div>

      <!-- 分组统计图 -->
      <div class="pending-summary-chart">
        <div id="pendingSummaryChart" class="chart"></div>
      </div>

      <!-- 明细表格 -->
      <div class="pending-detail-table">
        <div class="table-header">
          <h3>明细列表</h3>
          <div class="table-actions">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索单据"
              clearable
              @input="handleSearch"
              style="width: 200px"
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </div>
        </div>
        <VTableComponent
          :data="filteredPendingPaymentData"
          :width="tableWidth"
          :height="400"
          :show-filter="true"
          :editable="false"
          :enable-copy-paste="true"
          :auto-width="true"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import VTableComponent from "@/components/VTableComponent.vue";
import * as echarts from "echarts";
import { ElMessage } from "element-plus";
import { Search, Refresh } from '@element-plus/icons-vue'

// 页面状态
const dateRange = ref([
  new Date(new Date().setMonth(new Date().getMonth() - 1)),
  new Date(),
]);
const searchKeyword = ref("");
const tableWidth = ref(0);

// 日期快捷选项
const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 1);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setMonth(start.getMonth() - 3);
      return [start, end];
    },
  },
];

// 数据状态
const fundStatusData = ref([]);
const receiptData = ref([]);
const paymentPivotData = ref([]);
const pendingPaymentData = ref([]);
const fundDistributionData = ref([]);
const loading = ref(false);

// API请求函数
async function fetchCapitalFlowData() {
  if (!dateRange.value || dateRange.value.length !== 2) {
    ElMessage.warning('请选择日期范围');
    return;
  }

  loading.value = true;
  try {
    const startDate = dateRange.value[0].toISOString().split('T')[0];
    const endDate = dateRange.value[1].toISOString().split('T')[0];

    const response = await fetch('/api/capital-flow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        start_date: startDate,
        end_date: endDate
      })
    });

    if (!response.ok) {
      throw new Error('网络请求失败');
    }

    const data = await response.json();

    // 更新各个数据
    fundStatusData.value = data.fund_status_data;
    receiptData.value = data.receipt_data;
    paymentPivotData.value = data.payment_pivot_data;
    pendingPaymentData.value = data.pending_payment_data;
    fundDistributionData.value = data.fund_distribution_data;

    // 重新初始化图表
    setTimeout(() => {
      initFundDistributionChart();
      initPendingSummaryChart();
    }, 100);

    ElMessage.success('数据加载成功');
  } catch (error) {
    console.error('获取资金流动数据失败:', error);
    ElMessage.error('数据加载失败: ' + error.message);
  } finally {
    loading.value = false;
  }
}

// 计算在途付款总额
const pendingPaymentTotal = computed(() => {
  return pendingPaymentData.value.slice(1).reduce((total, row) => {
    return total + (typeof row[3] === 'number' ? row[3] : 0);
  }, 0);
});

// 过滤后的在途付款数据
const filteredPendingPaymentData = computed(() => {
  if (!searchKeyword.value) return pendingPaymentData.value;

  const searchTerm = searchKeyword.value.toLowerCase();
  return pendingPaymentData.value.filter((row, index) => {
    if (index === 0) return true; // 保留表头
    return row.some(
      (cell) => cell && cell.toString().toLowerCase().includes(searchTerm)
    );
  });
});

// 格式化数字
function formatNumber(num) {
  return new Intl.NumberFormat("zh-CN").format(num);
}

// 处理日期变化
function handleDateChange() {
  // 日期变化时不自动加载，等用户点击查询按钮
}

// 搜索数据
function searchData() {
  fetchCapitalFlowData();
}

// 重置筛选条件
function resetFilters() {
  dateRange.value = [
    new Date(new Date().setMonth(new Date().getMonth() - 1)),
    new Date(),
  ];
  searchKeyword.value = "";
  // 清空数据
  fundStatusData.value = [];
  receiptData.value = [];
  paymentPivotData.value = [];
  pendingPaymentData.value = [];
  fundDistributionData.value = [];
  ElMessage.success("已重置筛选条件");
}

// 处理搜索
function handleSearch() {
  // 实时搜索，无需额外操作
}

// 初始化资金分布饼图
function initFundDistributionChart() {
  const chartDom = document.getElementById("fundDistributionChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 使用从API获取的数据
  const data = fundDistributionData.value.length > 0 ? fundDistributionData.value : [
    { value: 0, name: '暂无数据' }
  ];

  const option = {
    title: {
      text: '资金分布情况',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '资金分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'outside',
          formatter: '{b}: {d}%'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: true
        },
        data: data,
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de']
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}

// 初始化在途付款分组统计图
function initPendingSummaryChart() {
  const chartDom = document.getElementById("pendingSummaryChart");
  if (!chartDom) return;

  const myChart = echarts.init(chartDom);

  // 按付款类型分组统计
  const typeStats = {};
  pendingPaymentData.value.slice(1).forEach(row => {
    const type = row[2]; // 付款类型
    const amount = row[3]; // 申请金额
    if (typeStats[type]) {
      typeStats[type] += amount;
    } else {
      typeStats[type] = amount;
    }
  });

  const categories = Object.keys(typeStats);
  const values = Object.values(typeStats);

  const option = {
    title: {
      text: '在途付款分组统计',
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        return `${params[0].name}<br/>金额: ¥${formatNumber(params[0].value)}`;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: categories,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: function(value) {
          return '¥' + (value / 10000).toFixed(0) + '万';
        }
      }
    },
    series: [
      {
        name: '付款金额',
        type: 'bar',
        data: values,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#83bff6' },
            { offset: 0.5, color: '#188df0' },
            { offset: 1, color: '#0a5cad' }
          ])
        },
        emphasis: {
          focus: 'series'
        }
      }
    ]
  };

  myChart.setOption(option);
  window.addEventListener("resize", () => myChart.resize());
}



// 页面加载时初始化
onMounted(() => {
  // 设置表格宽度
  tableWidth.value =
    document.querySelector(".capital-flow-container").offsetWidth - 40;

  // 监听窗口大小变化
  window.addEventListener("resize", () => {
    tableWidth.value =
      document.querySelector(".capital-flow-container").offsetWidth - 40;
  });

  // 页面加载时自动获取数据
  fetchCapitalFlowData();
});
</script>

<style scoped>
.capital-flow-container {
  width: 100%;
  height: 100%;
  padding: 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow-y: scroll;
  overflow-x: hidden;
}

h1 {
  margin-top: 0;
  margin-bottom: 32px;
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  text-align: center;
}

/* 时间选择器区域 */
.time-selector-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* 区域标题 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  position: relative;
}

.section-header h2::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 2px;
}

/* 各个功能区域 */
.fund-status-section,
.receipt-section,
.payment-section,
.chart-section,
.pending-payment-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #fafbfc;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
}

.fund-status-table,
.receipt-table,
.payment-table {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

/* 在途付款区域特殊样式 */
.summary-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.total-amount {
  font-size: 18px;
  font-weight: 600;
  color: #E6A23C;
  background: #FDF6EC;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #F5DAB1;
}

.pending-summary-chart {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.pending-detail-table {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.chart-container {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.chart {
  height: 400px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f2f5;
}

.table-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.table-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .capital-flow-container {
    padding: 16px;
  }

  .time-selector-section {
    flex-direction: column;
    gap: 12px;
  }

  .fund-status-section,
  .receipt-section,
  .payment-section,
  .chart-section,
  .pending-payment-section {
    padding: 16px;
    margin-bottom: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart {
    height: 300px;
  }
}

/* 动画效果 */
.fund-status-section,
.receipt-section,
.payment-section,
.chart-section,
.pending-payment-section {
  transition: all 0.3s ease;
}

.fund-status-section:hover,
.receipt-section:hover,
.payment-section:hover,
.chart-section:hover,
.pending-payment-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}
</style>
